import React from 'react';
import { MessageSquare, <PERSON>, <PERSON><PERSON><PERSON>, User } from 'lucide-react';

const DashboardLayout = ({ children, title = "Hyundai Bắc Ninh" }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md shadow-lg border-b border-white/20 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-4">
              <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl shadow-lg">
                <MessageSquare className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                  {title}
                </h1>
                <p className="text-sm text-gray-500 font-medium">Theo dõi hiệu suất thông báo</p>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="hidden sm:flex items-center space-x-2 px-4 py-2 bg-white/60 rounded-full border border-white/40 shadow-sm">
                <Bell className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-gray-600 font-medium">
                  {new Date().toLocaleDateString('vi-VN', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </span>
              </div>

              {/* Notification Bell */}
              <div className="relative">
                <button className="p-2 bg-white/60 rounded-full border border-white/40 shadow-sm hover:bg-white/80 transition-all duration-200">
                  <Bell className="w-5 h-5 text-gray-600" />
                </button>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-xs text-white font-bold">3</span>
                </div>
              </div>

              {/* Settings */}
              <button className="p-2 bg-white/60 rounded-full border border-white/40 shadow-sm hover:bg-white/80 transition-all duration-200">
                <Settings className="w-5 h-5 text-gray-600" />
              </button>

              {/* User Avatar */}
              <div className="relative group">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer group-hover:scale-105 transition-transform duration-200">
                  <span className="text-white font-semibold text-sm">HY</span>
                </div>
                {/* Tooltip */}
                <div className="absolute right-0 top-12 bg-black text-white text-xs rounded-lg px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                  Hyundai Admin
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </main>
    </div>
  );
};

export default DashboardLayout;
